const Feed = require("../models/feed.model");
const { deleteFile } = require("../middlewares/upload.middleware");

// Create a new feed post
const createFeed = async (req, res) => {
  try {
    const { title, description, category } = req.body;
    const userId = req.userId;

    // Prepare feed data
    const feedData = {
      userId,
      title,
      description,
      category,
    };

    // Add image if file was uploaded
    if (req.file) {
      feedData.image = req.file.filename;
    }

    // Create feed
    const feed = await Feed.create(feedData);

    // Populate user details for response
    await feed.populate("userId", "firstName lastName profilePic");

    res.status(201).json({
      success: true,
      message: "Feed post created successfully",
      data: {
        feed: {
          ...feed.toObject(),
          imageUrl: feed.image
            ? `${process.env.BASE_URL}/images/${feed.image}`
            : null,
          userProfilePic: feed.userId.profilePic
            ? `${process.env.BASE_URL}/images/${feed.userId.profilePic}`
            : null,
        },
      },
    });
  } catch (error) {
    // Clean up uploaded file if feed creation failed
    if (req.file) {
      deleteFile(req.file.path);
    }

    console.error("Create feed error:", error);
    res.status(500).json({
      success: false,
      message: "Failed to create feed post",
      error: process.env.NODE_ENV === "development" ? error.message : undefined,
    });
  }
};

// Get all feeds with pagination
const getAllFeeds = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    // Build filter object
    const filter = { isActive: true };
    if (req.query.category) {
      filter.category = req.query.category;
    }

    // Get feeds with pagination
    const feeds = await Feed.find(filter)
      .populate("userId", "firstName lastName profilePic")
      .populate("comments.userId", "firstName lastName profilePic")
      .sort({ postedAt: -1 })
      .skip(skip)
      .limit(limit);

    // Get total count for pagination
    const totalFeeds = await Feed.countDocuments(filter);
    const totalPages = Math.ceil(totalFeeds / limit);

    // Add image URLs and check if current user liked the post
    const currentUserId = req.userId;
    const feedsWithDetails = feeds.map((feed) => {
      const feedObj = feed.toObject();
      return {
        ...feedObj,
        imageUrl: feed.image
          ? `${process.env.BASE_URL}/images/${feed.image}`
          : null,
        userProfilePic: feed.userId?.profilePic
          ? `${process.env.BASE_URL}/images/${feed.userId.profilePic}`
          : null,
        isLikedByCurrentUser: currentUserId
          ? feed.likes.includes(currentUserId)
          : false,
        comments: feedObj.comments.map((comment) => ({
          ...comment,
          userProfilePic: comment.userId?.profilePic
            ? `${process.env.BASE_URL}/images/${comment.userId?.profilePic}`
            : null,
        })),
      };
    });

    res.status(200).json({
      success: true,
      data: {
        feeds: feedsWithDetails,
        pagination: {
          currentPage: page,
          totalPages,
          totalFeeds,
          hasNextPage: page < totalPages,
          hasPrevPage: page > 1,
        },
      },
    });
  } catch (error) {
    console.error("Get feeds error:", error);
    res.status(500).json({
      success: false,
      message: "Failed to fetch feeds",
      error: process.env.NODE_ENV === "development" ? error.message : undefined,
    });
  }
};

// Get single feed by ID
const getFeedById = async (req, res) => {
  try {
    const { id } = req.params;

    const feed = await Feed.findOne({ _id: id, isActive: true })
      .populate("userId", "firstName lastName profilePic email")
      .populate("comments.userId", "firstName lastName profilePic");

    if (!feed) {
      return res.status(404).json({
        success: false,
        message: "Feed not found",
      });
    }

    const currentUserId = req.userId;
    const feedObj = feed.toObject();

    res.status(200).json({
      success: true,
      data: {
        feed: {
          ...feedObj,
          imageUrl: feed.image
            ? `${process.env.BASE_URL}/images/${feed.image}`
            : null,
          userProfilePic: feed.userId.profilePic
            ? `${process.env.BASE_URL}/images/${feed.userId.profilePic}`
            : null,
          isLikedByCurrentUser: currentUserId
            ? feed.likes.includes(currentUserId)
            : false,
          comments: feedObj.comments.map((comment) => ({
            ...comment,
            userProfilePic: comment.userId.profilePic
              ? `${process.env.BASE_URL}/images/${comment.userId.profilePic}`
              : null,
          })),
        },
      },
    });
  } catch (error) {
    console.error("Get feed error:", error);
    res.status(500).json({
      success: false,
      message: "Failed to fetch feed",
      error: process.env.NODE_ENV === "development" ? error.message : undefined,
    });
  }
};

// Like/Unlike a feed post
const toggleLike = async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.userId;

    const feed = await Feed.findOne({ _id: id, isActive: true });

    if (!feed) {
      return res.status(404).json({
        success: false,
        message: "Feed not found",
      });
    }

    const isLiked = feed.likes.includes(userId);

    if (isLiked) {
      // Remove like
      feed.likes = feed.likes.filter(
        (likeUserId) => !likeUserId.equals(userId)
      );
    } else {
      // Add like
      feed.likes.push(userId);
    }

    await feed.save();

    res.status(200).json({
      success: true,
      message: isLiked ? "Like removed" : "Like added",
      data: {
        isLiked: !isLiked,
        likeCount: feed.likes.length,
      },
    });
  } catch (error) {
    console.error("Toggle like error:", error);
    res.status(500).json({
      success: false,
      message: "Failed to toggle like",
      error: process.env.NODE_ENV === "development" ? error.message : undefined,
    });
  }
};

// Add comment to a feed post
const addComment = async (req, res) => {
  try {
    const { id } = req.params;
    const { comment } = req.body;
    const userId = req.userId;

    const feed = await Feed.findOne({ _id: id, isActive: true });

    if (!feed) {
      return res.status(404).json({
        success: false,
        message: "Feed not found",
      });
    }

    // Add comment
    const newComment = {
      userId,
      comment,
      createdAt: new Date(),
    };

    feed.comments.push(newComment);
    await feed.save();

    // Populate the new comment with user details
    await feed.populate("comments.userId", "firstName lastName profilePic");

    const addedComment = feed.comments[feed.comments.length - 1];

    res.status(201).json({
      success: true,
      message: "Comment added successfully",
      data: {
        comment: {
          ...addedComment.toObject(),
          userProfilePic: addedComment.userId.profilePic
            ? `${process.env.BASE_URL}/images/${addedComment.userId.profilePic}`
            : null,
        },
        commentCount: feed.comments.length,
      },
    });
  } catch (error) {
    console.error("Add comment error:", error);
    res.status(500).json({
      success: false,
      message: "Failed to add comment",
      error: process.env.NODE_ENV === "development" ? error.message : undefined,
    });
  }
};

// Delete a feed post (user can only delete their own posts)
const deleteFeed = async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.userId;

    const feed = await Feed.findOne({ _id: id, userId, isActive: true });

    if (!feed) {
      return res.status(404).json({
        success: false,
        message: "Feed not found or you are not authorized to delete this post",
      });
    }

    // Delete associated image file
    if (feed.image) {
      const imagePath = path.join(__dirname, "../uploads", feed.image);
      deleteFile(imagePath);
    }

    // Soft delete - mark as inactive
    feed.isActive = false;
    await feed.save();

    res.status(200).json({
      success: true,
      message: "Feed post deleted successfully",
    });
  } catch (error) {
    console.error("Delete feed error:", error);
    res.status(500).json({
      success: false,
      message: "Failed to delete feed post",
      error: process.env.NODE_ENV === "development" ? error.message : undefined,
    });
  }
};

module.exports = {
  createFeed,
  getAllFeeds,
  getFeedById,
  toggleLike,
  addComment,
  deleteFeed,
};
