const router = require("express").Router();
const authController = require("../controllers/auth.controller");
const {
  authLimiter,
  forgotPasswordLimiter,
  validateRegister,
  validateLogin,
  validateForgotPassword,
} = require("../middlewares/security.middleware");
const { uploadSingle, cleanupOnError } = require("../middlewares/upload.middleware");

// Authentication routes
router.post(
  "/register",
  authLimiter,
  uploadSingle("profilePic"),
  cleanupOnError,
  validateRegister,
  authController.register
);
router.post("/login", authLimiter, validateLogin, authController.login);
router.post(
  "/forgot-password",
  forgotPasswordLimiter,
  validateForgotPassword,
  authController.forgotPassword
);

module.exports = router;
